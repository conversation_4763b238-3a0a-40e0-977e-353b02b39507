[Launcher]
IniError=Initialisierungsdatei konnte nicht gelesen werden.
UnsupportedArch=Nicht unterstützte Betriebssystemarchitektur. Das Spiel läuft nur unter 64-Bit-Windows.
IntroPart1=BattlEye ist eine Anti-Cheat-Software, die in Online-Multiplayer-Spielen eingesetzt wird. Sie verhindert und erkennt aktiv Cheats und verbannt Cheater vom Spiel auf BattlEye-geschützten Servern.\n\nUm sein Ziel zu erreichen, installiert BattlEye einen Systemdienst, der nur aktiv ist, während das Spiel läuft. Der BattlEye-Systemdienst installiert und lädt außerdem vorübergehend einen Kernel-Mode-Treiber.\n\nObwohl BattlEye aufgrund seiner Natur Zugriff auf große Teile Ihres Systems haben muss, stellt es sicher, dass Ihre Privatsphäre nicht verletzt wird.\nBattlEye ist weder an Ihren persönlichen Daten (Dokumente, Passwörter, Kreditkartendaten usw.) noch an anderen Informationen interessiert, die für Sie als Person privat sind.\nAlles, was BattlEye tut, dient ausschließlich dem Ziel, Betrug zu verhindern und aufzudecken.
IntroPart2=Um die EULA anzuzeigen, BattlEye zu installieren und auf BattlEye-geschützten Servern spielen zu können, klicken Sie bitte unten auf "Installieren". Andernfalls klicken Sie auf "Abbrechen", um ohne BattlEye fortzufahren.
IntroPart2Alt=Beachten Sie, dass BattlEye zum Spielen des Spiels erforderlich ist. Um die EULA anzuzeigen und BattlEye zu installieren, klicken Sie bitte unten auf "Installieren". Andernfalls klicken Sie zum Beenden auf "Abbrechen".
Install=Installieren
Cancel=Abbrechen
DoNotAsk=Diese Meldung nicht mehr anzeigen.
EULAHeader=Um BattlEye zu installieren, müssen Sie die Bedingungen der Endbenutzer-Lizenzvereinbarung (EULA) akzeptieren.
Accept=Ich akzeptiere
PrivacyError=Der Datenschutztext konnte nicht geöffnet werden.
Updating=Aktualisieren...
UpdateError=Aktualisierung fehlgeschlagen (%u, %u). Bitte überprüfen Sie Ihre Internetverbindung und/oder Firewall.
InstallingService=BattlEye-Dienst wird installiert...
ServiceInstalled=Der BattlEye-Dienst wurde erfolgreich installiert.
ServiceInstallError=Der BattlEye-Dienst konnte nicht installiert werden (%u, %x).
StartingService=BattlEye-Dienst wird gestartet...
ServiceStartError=Der BattlEye-Dienst konnte nicht gestartet werden (%u).
ServiceCommError=Die Kommunikation mit dem BattlEye-Dienst ist fehlgeschlagen.
ServiceInitError=Der BattlEye-Dienst konnte nicht initialisiert werden: %.*hs.
LaunchingGame=Spiel wird gestartet...
GameLaunchError=Das Spiel konnte nicht gestartet werden.
FileBlocksNote=Hinweis: Dateiblockierungen können ignoriert werden, wenn sie keine Probleme mit dem Spiel verursachen.
FileBlockInfo=[INFO] Das Laden der Datei wurde blockiert: "%.*ls".
UNCFileBlockInfo=[INFO] Das Laden einer Datei mit nicht unterstütztem UNC-Netzwerkpfad wurde blockiert: "%.*ls".
DisallowedDriver=Unzulässiger Treiber: "%.*ls". Bitte entladen Sie ihn oder starten Sie Ihr System neu.
WindowsKernelMod=Windows-Kernel-Modifikation erkannt. Bitte reparieren oder installieren Sie Ihr System neu.
UninstallPrompt=Möchten Sie den BattlEye-Dienst von Ihrem System deinstallieren?
ServiceUninstalled=Der BattlEye-Dienst wurde deinstalliert.
ServiceUninstallError=Die Deinstallation des BattlEye-Dienstes ist fehlgeschlagen.