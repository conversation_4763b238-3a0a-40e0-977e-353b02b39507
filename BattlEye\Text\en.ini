[Launcher]
IniError=Failed to read initialization file.
UnsupportedArch=Unsupported operating system architecture. Game only runs on 64-bit Windows.
IntroPart1=BattlEye is anti-cheat software used in online multiplayer games that actively prevents and detects cheating and bans cheaters from playing on BattlEye-protected servers.\n\nIn order to achieve its goal BattlEye installs a system service that is only active while the game is running. The BattlEye system service also temporarily installs and loads a kernel-mode driver.\n\nWhile due to its nature BattlEye needs to have access to great parts of your system, it ensures that your privacy is not violated.\nBattlEye is neither interested in your personal data (documents, passwords, credit card details, etc.) nor any other information that is private to you as an individual.\nEverything that BattlEye does is solely aimed at achieving its goal of preventing and detecting cheating.
IntroPart2=To show the EULA, install BattlEye and be able to play on BattlEye-protected servers, please click "Install" below. Otherwise click "Cancel" to proceed without BattlEye.
IntroPart2Alt=Note that BattlEye is required to play the game. To show the EULA and install Bat<PERSON><PERSON>ye, please click "Install" below. Otherwise click "Cancel" to quit.
Install=Install
Cancel=Cancel
DoNotAsk=Do not ask me again.
EULAHeader=To install BattlEye, you must accept the terms of the End-User License Agreement (EULA).
Accept=I accept
PrivacyError=Failed to open privacy text.
Updating=Updating...
UpdateError=Update failed (%u, %u). Please check your internet connection and/or firewall.
InstallingService=Installing BattlEye Service...
ServiceInstalled=BattlEye Service has been successfully installed.
ServiceInstallError=Failed to install BattlEye Service (%u, %x).
StartingService=Starting BattlEye Service...
ServiceStartError=Failed to start BattlEye Service (%u).
ServiceCommError=Failed to communicate with BattlEye Service.
ServiceInitError=Failed to initialize BattlEye Service: %.*hs.
LaunchingGame=Launching game...
GameLaunchError=Failed to launch game.
FileBlocksNote=Note: File blocks can be ignored if they don't cause problems with the game.
FileBlockInfo=[INFO] Blocked loading of file: "%.*ls".
UNCFileBlockInfo=[INFO] Blocked loading of file with unsupported UNC network path: "%.*ls".
DisallowedDriver=Disallowed driver: "%.*ls". Please unload it or reboot your system.
WindowsKernelMod=Windows Kernel modification detected. Please repair or reinstall your system.
UninstallPrompt=Would you like to uninstall the BattlEye Service from your system?
ServiceUninstalled=BattlEye Service has been uninstalled.
ServiceUninstallError=Failed to uninstall BattlEye Service.