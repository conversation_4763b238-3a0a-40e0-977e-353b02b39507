[Launcher]
IniError=Fallo en la lectura del archivo de inicialización.
UnsupportedArch=Arquitectura del sistema operativo no compatible. El juego solo funciona en Windows de 64 bits.
IntroPart1=BattlEye es un software antitrampas utilizado en juegos multijugador en línea que previene y detecta activamente las trampas y prohíbe a los tramposos jugar en servidores protegidos por BattlEye.\n\nPara lograr su objetivo, BattlEye instala un servicio del sistema que solo está activo mientras el juego está en marcha. El servicio de sistema de BattlEye también instala y carga temporalmente un controlador en modo kernel.\n\nAunque debido a su naturaleza BattlEye necesita tener acceso a grandes partes de tu sistema, se asegura de que tu privacidad no sea violada.\nBattlEye no está interesado en tus datos personales (documentos, contraseñas, detalles de la tarjeta de crédito, etc.) ni en ninguna otra información que sea privada para ti como individuo.\nTodo lo que hace BattlEye está dirigido exclusivamente a lograr su objetivo de prevenir y detectar las trampas.
IntroPart2=Para mostrar el ALUF, instalar BattlEye y poder jugar en servidores protegidos por BattlEye, haz clic en "Instalar". De lo contrario, haz clic en "Cancelar" para continuar sin BattlEye.
IntroPart2Alt=Ten en cuenta que BattlEye es necesario para jugar al juego. Para mostrar el ALUF e instalar BattlEye, haz clic en "Instalar". De lo contrario, haz clic en "Cancelar" para salir.
Install=Instalar
Cancel=Cancelar
DoNotAsk=No volver a preguntar
EULAHeader=Para instalar BattlEye, debes aceptar los términos del Acuerdo de Licencia de Usuario Final (ALUF).
Accept=Acepto
PrivacyError=No se ha podido abrir el texto de privacidad.
Updating=Actualizando...
UpdateError=La actualización ha fallado (%u, %u). Compruebe su conexión a Internet y/o su cortafuegos.
InstallingService=Instalación del servicio BattlEye...
ServiceInstalled=El servicio BattlEye ha sido instalado con éxito.
ServiceInstallError=Fallo al instalar el servicio BattlEye (%u, %x).
StartingService=Iniciando el servicio BattlEye...
ServiceStartError=No se ha podido iniciar el servicio BattlEye (%u).
ServiceCommError=Fallo en la comunicación con el servicio BattlEye.
ServiceInitError=Fallo al inicializar el servicio BattlEye: %.*hs.
LaunchingGame=Iniciando el juego...
GameLaunchError=No se ha podido iniciar el juego.
FileBlocksNote=Nota: Los bloques de archivos pueden ser ignorados si no causan problemas con el juego.
FileBlockInfo=[INFO] Bloqueo de la carga del archivo: "%.*ls".
UNCFileBlockInfo=[INFO] Bloqueada la carga de un archivo con una ruta de red UNC no soportada: "%.*ls".
DisallowedDriver=Controlador deshabilitado: "%.*ls". Desinstálalo o reinicia el sistema.
WindowsKernelMod=Se ha detectado una modificación del Kernel de Windows. Repare o reinstale su sistema.
UninstallPrompt=¿Quieres desinstalar el servicio BattlEye de tu sistema?
ServiceUninstalled=El servicio BattlEye ha sido desinstalado.
ServiceUninstallError=No se ha podido desinstalar el servicio BattlEye.