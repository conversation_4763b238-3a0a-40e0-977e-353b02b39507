[Launcher]
IniError=Échec de la lecture du fichier d'initialisation.
UnsupportedArch=Architecture du système d'exploitation non prise en charge. Le jeu ne fonctionne que sur Windows 64 bits.
IntroPart1=BattlEye est un logiciel anti-triche utilisé dans les jeux multijoueurs en ligne qui empêche et détecte activement la tricherie et interdit aux tricheurs de jouer sur les serveurs protégés par BattlEye.\n\nAfin d'atteindre son objectif, BattlEye installe un service système qui n'est actif que lorsque le jeu est en cours. Le service système BattlEye installe et charge aussi temporairement un pilote en mode noyau.\n\nBien que, de par sa nature, BattlEye doive avoir accès à de grandes parties de votre système, il veille à ce que votre vie privée ne soit pas violée.\nBattlEye n'est pas intéressé par vos données personnelles (documents, mots de passe, détails de cartes de crédit, etc.) ni par aucune autre information qui vous est privée en tant qu'individu.\nTout ce que fait BattlEye est uniquement destiné à atteindre son objectif de prévention et de détection de la tricherie.
IntroPart2=Pour afficher le CLUF, installer BattlEye et pouvoir jouer sur des serveurs protégés par BattlEye, veuillez cliquer sur "Installer" ci-dessous. Sinon, cliquez sur "Annuler" pour continuer sans BattlEye.
IntroPart2Alt=Notez que BattlEye est nécessaire pour jouer au jeu. Pour afficher le CLUF et installer BattlEye, veuillez cliquer sur "Installer" ci-dessous. Sinon, cliquez sur "Annuler" pour quitter.
Install=Installer
Cancel=Annuler
DoNotAsk=Ne me demandez pas à nouveau.
EULAHeader=Pour installer BattlEye, vous devez accepter les termes du contrat de licence utilisateur final (CLUF).
Accept=J'accepte
PrivacyError=Impossible d'ouvrir le texte de confidentialité.
Updating=Mise à jour...
UpdateError=La mise à jour a échoué (%u, %u). Veuillez vérifier votre connexion Internet et/ou votre pare-feu.
InstallingService=Installation du service BattlEye...
ServiceInstalled=Le service BattlEye a été installé avec succès.
ServiceInstallError=Échec de l'installation du service BattlEye (%u, %x).
StartingService=Démarrage du service BattlEye...
ServiceStartError=Échec du démarrage du service BattlEye (%u).
ServiceCommError=Échec de la communication avec le service BattlEye.
ServiceInitError=Échec de l'initialisation du service BattlEye : %.*hs.
LaunchingGame=Lancement du jeu...
GameLaunchError=Impossible de lancer le jeu.
FileBlocksNote=Remarque : les blocs de fichiers peuvent être ignorés s'ils ne causent pas de problèmes dans le jeu.
FileBlockInfo=[INFO] Blocage du chargement du fichier : "%.*ls".
UNCFileBlockInfo=[INFO] Blocage du chargement d'un fichier avec un chemin d'accès réseau UNC non pris en charge : "%.*ls".
DisallowedDriver=Pilote non autorisé : "%.*ls". Veuillez le décharger ou redémarrer votre système.
WindowsKernelMod=Modification du noyau de Windows détectée. Veuillez réparer ou réinstaller votre système.
UninstallPrompt=Vous souhaitez désinstaller le Service BattlEye de votre système ?
ServiceUninstalled=Le service BattlEye a été désinstallé.
ServiceUninstallError=Échec de la désinstallation du service BattlEye.