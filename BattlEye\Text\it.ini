[Launcher]
IniError=Impossibile leggere il file di inizializzazione.
UnsupportedArch=Architettura del sistema operativo non supportata. Il gioco funziona solo su Windows a 64 bit.
IntroPart1=BattlEye è un software anti-cheat usato nei giochi multiplayer online che previene e rileva attivamente gli imbrogli e vieta ai bari di giocare sui server protetti da BattlEye.\n\nPer raggiungere il suo obiettivo BattlEye installa un servizio di sistema che è attivo solo mentre il gioco è in esecuzione. Il servizio di sistema BattlEye installa e carica anche temporaneamente un driver in modalità kernel.\n\nMentre a causa della sua natura BattlEye ha bisogno di avere accesso a grandi parti del vostro sistema, assicura che la vostra privacy non sia violata.\nBattlEye non è interessato ai vostri dati personali (documenti, password, dettagli della carta di credito, ecc.) né a qualsiasi altra informazione che sia privata di voi come individuo.\nTutto ciò che BattlEye fa è unicamente finalizzato a raggiungere il suo obiettivo di prevenire e rilevare gli imbrogli.
IntroPart2=Per mostrare l'EULA, installare BattlEye e poter giocare sui server protetti da BattlEye, clicca su "Installa" qui sotto. Altrimenti clicca "Annulla" per procedere senza BattlEye.
IntroPart2Alt=Nota che BattlEye è richiesto per giocare. Per mostrare l'EULA e installare BattlEye, clicca su "Installa" qui sotto. Altrimenti clicca su "Annulla" per uscire.
Install=Installa
Cancel=Annulla
DoNotAsk=Non chiedermelo più.
EULAHeader=Per installare BattlEye, devi accettare i termini dell'Accordo di licenza con l'utente finale (EULA).
Accept=Accetto
PrivacyError=Impossibile aprire il testo sulla privacy.
Updating=Aggiornamento...
UpdateError=Aggiornamento fallito (%u, %u). Controllare la connessione a Internet e/o il firewall.
InstallingService=Installazione del servizio BattlEye...
ServiceInstalled=BattlEye Service è stato installato con successo.
ServiceInstallError=Fallita l'installazione del servizio BattlEye (%u, %x).
StartingService=Avvio del servizio BattlEye...
ServiceStartError=Fallito l'avvio del servizio BattlEye (%u).
ServiceCommError=Impossibile comunicare con BattlEye Service.
ServiceInitError=Fallita l'inizializzazione del servizio BattlEye: %.*hs.
LaunchingGame=Avvio del gioco...
GameLaunchError=Fallito l'avvio del gioco.
FileBlocksNote=Nota: i blocchi di file possono essere ignorati se non causano problemi al gioco.
FileBlockInfo=[INFO] Caricamento bloccato del file: "%.*ls".
UNCFileBlockInfo=[INFO] Caricamento bloccato del file con percorso di rete UNC non supportato: "%.*ls".
DisallowedDriver=Driver non consentito: "%.*ls". Disattivalo o riavvia il sistema.
WindowsKernelMod=Rilevata modifica del kernel di Windows. Si prega di riparare o reinstallare il sistema.
UninstallPrompt=Vuoi disinstallare il servizio BattlEye dal tuo sistema?
ServiceUninstalled=BattlEye Service è stato disinstallato.
ServiceUninstallError=Fallita la disinstallazione di BattlEye Service.