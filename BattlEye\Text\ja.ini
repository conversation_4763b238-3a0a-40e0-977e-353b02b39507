[Launcher]
IniError=初期化ファイルの読み取りに失敗しました。
UnsupportedArch=サポートされていないオペレーティング・システム・アーキテクチャ。ゲームは64ビットのWindowsでのみ動作します。
IntroPart1=BattlEyeは、オンラインマルチプレイヤーゲームで使用されるアンチチートソフトウェアで、不正行為を積極的に防止・検出し、BattlEyeで保護されたサーバーでの不正行為を禁止します。\n\nBattlEyeは、その目的を達成するために、ゲームが実行されている間だけアクティブになるシステムサービスをインストールします。また、BattlEyeのシステムサービスは、一時的にカーネルモードのドライバーをインストールしてロードします。\n\nBattlEyeは、その性質上、お客様のシステムの大部分にアクセスする必要がありますが、お客様のプライバシーが侵害されないようにしています。\nBattlEyeは、お客様の個人的なデータ（文書、パスワード、クレジットカードの詳細など）や、個人としてのお客様のプライベートな情報には関心がありません。\nBattlEyeが行うすべてのことは、不正行為の防止と検出という目的を達成するためだけに行われます。
IntroPart2=EULAを表示し、BattlEyeをインストールして、BattlEyeで保護されたサーバーでプレイできるようにするには、以下の「インストール」をクリックしてください。BattlEyeをインストールしない場合は、「キャンセル」をクリックしてください。
IntroPart2Alt=なお、ゲームをプレイするには「BattlEye」が必要です。EULAを表示してBattlEyeをインストールするには、以下の「インストール」をクリックしてください。インストールしない場合は、「キャンセル」をクリックしてください。
Install=インストール
Cancel=キャンセル
DoNotAsk=二度と聞かないでください。
EULAHeader=BattlEyeをインストールするには、エンドユーザー使用許諾契約書（EULA）の条項に同意する必要があります。
Accept=受け入れる
PrivacyError=プライバシーテキストを開くのに失敗しました。
Updating=更新中...
UpdateError=アップデートに失敗しました (%u, %u)。インターネット接続やファイアウォールを確認してください。
InstallingService=BattlEye Serviceのインストール...
ServiceInstalled=BattlEye Serviceのインストールが完了しました。
ServiceInstallError=BattlEye Serviceのインストールに失敗しました (%u, %x)。
StartingService=BattlEye Serviceの開始...
ServiceStartError=BattlEye Serviceの起動に失敗しました (%u)。
ServiceCommError=BattlEye Serviceとの通信に失敗しました。
ServiceInitError=BattlEye Serviceの初期化に失敗しました： %.*hs.
LaunchingGame=ゲームを起動する...
GameLaunchError=ゲームの起動に失敗しました。
FileBlocksNote=注：ファイルブロックは、ゲームに問題がなければ無視しても構いません。
FileBlockInfo=[INFO] ファイルの読み込みがブロックされました："%.*ls".
UNCFileBlockInfo=[INFO] サポートされていない UNC ネットワークパスを持つファイルの読み込みがブロックされました："%.*ls".
DisallowedDriver=許可されていないドライバー："%.*ls". アンロードするか、システムを再起動してください。
WindowsKernelMod=Windows カーネルの変更が検出されました。システムを修復または再インストールしてください。
UninstallPrompt=お使いのシステムからBattlEye Serviceをアンインストールしますか？
ServiceUninstalled=BattlEye Serviceがアンインストールされました。
ServiceUninstallError=BattlEye Serviceのアンインストールに失敗しました。