[Launcher]
IniError=초기화 파일을 읽지 못했습니다.
UnsupportedArch=지원되지 않는 운영 체제 아키텍처입니다. 게임은 64비트 Windows에서만 실행됩니다.
IntroPart1=BattlEye는 부정 행위를 적극적으로 방지 및 감지하고 부정 행위자가 BattlEye로 보호되는 서버에서 플레이하는 것을 금지하는 온라인 멀티플레이어 게임에 사용되는 치트 방지 소프트웨어입니다.\n\n목표를 달성하기 위해 BattlEye는 게임이 실행되는 동안에만 활성화되는 시스템 서비스를 설치합니다. BattlEye 시스템 서비스는 또한 일시적으로 커널 모드 드라이버를 설치하고 로드합니다.\n\n그 특성상 BattlEye는 시스템의 많은 부분에 액세스해야 하지만 개인 정보가 침해되지 않도록 합니다.\nBattlEye는 귀하의 개인 데이터(문서, 비밀번호, 신용 카드 세부 정보 등) 또는 귀하에게 개인으로서 사적인 기타 정보에 관심이 없습니다.\nBattlEye가 하는 모든 일은 부정 행위를 방지하고 탐지하는 데에만 그 목적이 있습니다.
IntroPart2=EULA를 표시하고, BattlEye를 설치하고, BattlEye로 보호된 서버에서 플레이하려면 아래의 "설치"를 클릭하십시오. BattlEye 없이 계속 진행하려면 "취소"를 클릭하십시오.
IntroPart2Alt=게임을 플레이하려면 BattlEye가 필요합니다. EULA를 표시하고 BattlEye를 설치하려면 아래의 "설치"를 클릭하십시오. 그렇지 않으면 "취소"를 클릭하여 종료하십시오.
Install=설치
Cancel=취소
DoNotAsk=다시 묻지 않기
EULAHeader=BattlEye를 설치하려면 최종 사용자 사용권 계약(EULA) 조건에 동의해야 합니다.
Accept=동의 함
PrivacyError=개인정보 보호 텍스트를 열지 못했습니다.
Updating=업데이트 중...
UpdateError=업데이트에 실패했습니다 (%u, %u). 인터넷 연결 및/또는 방화벽을 확인하십시오.
InstallingService=BattlEye 서비스 설치 중...
ServiceInstalled=BattlEye 서비스가 성공적으로 설치되었습니다.
ServiceInstallError=BattlEye 서비스를 설치하지 못했습니다 (%u, %x).
StartingService=BattlEye 서비스 시작 중...
ServiceStartError=BattlEye 서비스를 시작하지 못했습니다 (%u).
ServiceCommError=BattlEye 서비스와 통신하지 못했습니다.
ServiceInitError=BattlEye 서비스 초기화 실패: %.*hs.
LaunchingGame=게임 실행 중...
GameLaunchError=게임 실행에 실패했습니다.
FileBlocksNote=참고: 파일 차단은 게임에 문제를 일으키지 않는 경우 무시할 수 있습니다.
FileBlockInfo=[정보] 파일 로드 차단: "%.*ls".
UNCFileBlockInfo=[정보] 지원되지 않는 UNC 네트워크 경로가 있는 파일 로드가 차단되었습니다: "%.*ls".
DisallowedDriver=허용되지 않는 드라이버: "%.*ls". 해당 드라이버를 언로드하거나 시스템을 재부팅하십시오.
WindowsKernelMod=Windows 커널 수정이 감지되었습니다. 시스템을 수리하거나 다시 설치하십시오.
UninstallPrompt=시스템에서 BattlEye 서비스를 제거하시겠습니까?
ServiceUninstalled=BattlEye 서비스가 제거되었습니다.
ServiceUninstallError=BattlEye 서비스를 제거하지 못했습니다.