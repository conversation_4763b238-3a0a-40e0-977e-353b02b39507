[Launcher]
IniError=Не удалось прочитать файл инициализации.
UnsupportedArch=Неподдерживаемая архитектура операционной системы. Игра запускается только на 64-битной Windows.
IntroPart1=BattlEye - это античитерское программное обеспечение, используемое в многопользовательских онлайн-играх, которое активно предотвращает и обнаруживает читерство и запрещает читерам играть на серверах, защищенных BattlEye.\n\nДля достижения своей цели BattlEye устанавливает системную службу, которая активна только во время игры. Системная служба BattlEye также временно устанавливает и загружает драйвер в режиме ядра.\n\nХотя в силу своей природы BattlEye должен иметь доступ к большим частям вашей системы, он гарантирует, что ваша конфиденциальность не будет нарушена.\nBattlEye не интересуют ни ваши личные данные (документы, пароли, данные кредитных карт и т.д.), ни любая другая информация, которая является частной для вас как личности.\nВсе, что делает BattlEye, направлено исключительно на достижение цели - предотвращение и обнаружение мошенничества.
IntroPart2=Чтобы показать EULA, установить BattlEye и получить возможность играть на серверах, защищенных BattlEye, пожалуйста, нажмите "Установить" ниже. В противном случае нажмите "Отмена", чтобы продолжить игру без BattlEye.
IntroPart2Alt=Обратите внимание, что для игры требуется BattlEye. Чтобы показать EULA и установить BattlEye, пожалуйста, нажмите "Установить" ниже. В противном случае нажмите "Отмена", чтобы выйти из игры.
Install=Установить
Cancel=Отмена
DoNotAsk=Не спрашивайте меня больше.
EULAHeader=Чтобы установить BattlEye, вы должны принять условия лицензионного соглашения с конечным пользователем (EULA).
Accept=Я принимаю
PrivacyError=Не удалось открыть текст конфиденциальности.
Updating=Обновление...
UpdateError=Не удалось загрузить обновление (%u, %u). Пожалуйста, проверьте подключение к Интернету и/или брандмауэр.
InstallingService=Установка BattlEye...
ServiceInstalled=Программа BattlEye успешно установлена.
ServiceInstallError=Не удалось установить BattlEye (%u, %x).
StartingService=Запуск BattlEye...
ServiceStartError=Не удалось запустить BattlEye (%u).
ServiceCommError=Не удалось установить связь с BattlEye.
ServiceInitError=Не удалось инициализировать BattlEye: %.*hs.
LaunchingGame=Запуск игры...
GameLaunchError=Не удалось запустить игру.
FileBlocksNote=Примечание: блоки файлов можно игнорировать, если они не вызывают проблем в игре.
FileBlockInfo=[INFO] Заблокирована загрузка файла: "%.*ls".
UNCFileBlockInfo=[INFO] Заблокирована загрузка файла с неподдерживаемым сетевым путем UNC: "%.*ls".
DisallowedDriver=Запрещенный драйвер: "%.*ls". Пожалуйста, выгрузите его или перезагрузите систему.
WindowsKernelMod=Обнаружена модификация ядра Windows. Пожалуйста, исправьте или переустановите систему.
UninstallPrompt=Вы хотите удалить программу BattlEye из вашей системы?
ServiceUninstalled=Программа BattlEye удалена.
ServiceUninstallError=Не удалось удалить BattlEye.