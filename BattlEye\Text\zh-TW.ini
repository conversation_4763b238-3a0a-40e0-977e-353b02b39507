[Launcher]
IniError=無法讀取初始化檔案。
UnsupportedArch=不支持的作業系統架構。 遊戲只能在 64 位 Windows 上執行。
IntroPart1=BattlEye 是用於線上多人遊戲的反作弊軟件，可主動防止和檢測作弊並禁止作弊者在受 BattlEye 保護的服務器上玩遊戲。\n\n為了實現其目標，BattlEye 安裝了一個系統服務，該服務僅在遊戲執行時處於活動狀態。 BattlEye 系統服務還會臨時安裝和載入內核模式驅動程式。\n\n雖然由於 BattlEye 的性質，它需要讀取您系統的大部分內容，但它可以確保您的隱私不受侵犯。\nBattlEye 對您的個人資料（文件、密碼、信用卡詳細資訊等）和任何其他對您個人而言是私密的資訊都不感興趣。\nBattlEye 所做的一切都只是為了實現其防止和檢測作弊的目標。
IntroPart2=要顯示 EULA，安裝 BattlEye 並能夠在受 BattlEye 保護的服務器上遊玩，請點選下面的「安裝」。 否則點選「取消」以在不使用 BattlEye 的情況下繼續。
IntroPart2Alt=請注意，需要 BattlEye 才能玩遊戲。 要顯示 EULA 並安裝 BattlEye，請點選下面的「安裝」。 否則點選「取消」退出。
Install=安裝
Cancel=取消
DoNotAsk=不要再問我。
EULAHeader=要安裝 BattlEye，您必須接受最終用戶許可協議（EULA）的條款。
Accept=我接受
PrivacyError=無法打開隱私權文件。
Updating=更新中…
UpdateError=更新失敗（%u, %u）。請檢查您的網路連接和/或防火牆。
InstallingService=正在安裝 BattlEye 服務…
ServiceInstalled=BattlEye 服務已成功安裝。
ServiceInstallError=無法安裝 BattlEye 服務（%u, %x）。
StartingService=正在啟動 BattlEye 服務…
ServiceStartError=無法啟動 BattlEye 服務（%u）。
ServiceCommError=無法與 BattlEye 服務通信。
ServiceInitError=無法初始化 BattlEye 服務：%.*hs。
LaunchingGame=正在啟動遊戲…
GameLaunchError=無法啟動遊戲。
FileBlocksNote=注意：如果檔案封鎖不會導致遊戲出現問題，則可以忽略它們。
FileBlockInfo=[資訊] 檔案載入被封鎖：「%.*ls」。
UNCFileBlockInfo=[資訊] 封鎖載入具有不受支援的 UNC 網路路徑的檔案：「%.*ls」。
DisallowedDriver=不允許的驅動程式：「%.*ls」。請將其移除或重新啟動您的系統。
WindowsKernelMod=檢測到 Windows 核心修改。請修復或重新安裝您的系統。
UninstallPrompt=您想從系統中解除安裝 BattlEye 服務嗎？
ServiceUninstalled=BattlEye 服務已被解除安裝。
ServiceUninstallError=無法解除安裝 BattlEye 服務。