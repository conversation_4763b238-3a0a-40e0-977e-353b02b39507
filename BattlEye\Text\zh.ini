[Launcher]
IniError=无法读取初始化文件。
UnsupportedArch=不支持的操作系统架构。 游戏只能在 64 位 Windows 上运行。
IntroPart1=BattlEye 是用于在线多人游戏的反作弊软件，可主动防止和检测作弊并禁止作弊者在受 BattlEye 保护的服务器上玩游戏。\n\n为了实现其目标，BattlEye 安装了一个系统服务，该服务仅在游戏运行时处于活动状态。 BattlEye 系统服务还会临时安装和加载内核模式驱动程序。\n\n虽然由于 BattlEye 的性质，它需要访问您系统的大部分内容，但它可以确保您的隐私不受侵犯。\nBattlEye 对您的个人数据（文件、密码、信用卡详细信息等）和任何其他对您个人而言是私密的信息都不感兴趣。\nBattlEye 所做的一切都只是为了实现其防止和检测作弊的目标。
IntroPart2=要显示 EULA，安装 BattlEye 并能够在受 BattlEye 保护的服务器上播放，请单击下面的“安装”。 否则单击“取消”以在不使用 BattlEye 的情况下继续。
IntroPart2Alt=请注意，需要 BattlEye 才能玩游戏。 要显示 EULA 并安装 BattlEye，请单击下面的“安装”。 否则单击“取消”退出。
Install=安装
Cancel=取消
DoNotAsk=不要再问我。
EULAHeader=要安装 BattlEye，您必须接受最终用户许可协议 (EULA) 的条款。
Accept=我接受
PrivacyError=无法打开隐私文本。
Updating=更新中...
UpdateError=更新失败 (%u, %u)。 请检查您的互联网连接和/或防火墙。
InstallingService=安装 BattlEye 服务...
ServiceInstalled=BattlEye 服务已成功安装。
ServiceInstallError=无法安装 BattlEye 服务 (%u, %x)。
StartingService=启动 BattlEye 服务...
ServiceStartError=无法启动 BattlEye 服务 (%u)。
ServiceCommError=无法与 BattlEye 服务通信。
ServiceInitError=无法初始化 BattlEye 服务：%.*hs。
LaunchingGame=启动游戏...
GameLaunchError=无法启动游戏。
FileBlocksNote=注意：如果文件屏蔽不会导致游戏出现问题，则可以忽略它们。
FileBlockInfo=[信息] 文件加载被阻止：“%.*ls”。
UNCFileBlockInfo=[信息] 阻止加载具有不受支持的 UNC 网络路径的文件：“%.*ls”。
DisallowedDriver=不允许的驱动程序：“%.*ls”。 请关闭它或重新启动系统。
WindowsKernelMod=检测到 Windows 内核修改。 请修复或重新安装您的系统。
UninstallPrompt=您想从系统中卸载 BattlEye 服务吗？
ServiceUninstalled=BattlEye 服务已被卸载。
ServiceUninstallError=无法卸载 BattlEye 服务。