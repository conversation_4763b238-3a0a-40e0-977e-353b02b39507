cmake_minimum_required(VERSION 3.16)
project(GTA5_Offline_Mods)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type to Release by default
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Include directories
include_directories(src)

# Source files
set(SOURCES
    src/MemoryUtils.cpp
    src/BasicMods.cpp
)

set(HEADERS
    src/MemoryOffsets.h
    src/BasicMods.h
)

# Console application target
add_executable(GTA5_Mods_Console
    ${SOURCES}
    ${HEADERS}
    src/main.cpp
)

# DLL target for injection
add_library(GTA5_Mods_DLL SHARED
    ${SOURCES}
    ${HEADERS}
    src/main.cpp
)

# Set DLL preprocessor definition
target_compile_definitions(GTA5_Mods_DLL PRIVATE _DLL)

# Link libraries
if(WIN32)
    target_link_libraries(GTA5_Mods_Console kernel32 user32)
    target_link_libraries(GTA5_Mods_DLL kernel32 user32)
endif()

# Set output directories
set_target_properties(GTA5_Mods_Console PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(GTA5_Mods_DLL PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy README to build directory
configure_file(${CMAKE_SOURCE_DIR}/README.md ${CMAKE_BINARY_DIR}/README.md COPYONLY)

# Installation
install(TARGETS GTA5_Mods_Console GTA5_Mods_DLL
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION bin
)

install(FILES README.md DESTINATION .)

# Print build information
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
