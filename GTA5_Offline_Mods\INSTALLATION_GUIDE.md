# GTA V Offline Mods - Installation Guide

## Prerequisites

### System Requirements
- Windows 10/11 (64-bit)
- GTA V version 1.0.3586.0 (Steam/Rockstar Games)
- Visual Studio 2019/2022 with C++ development tools
- CMake 3.16 or later

### Required Downloads
1. **ScriptHookV v3586.0** - Download from: http://www.dev-c.com/gtav/scripthookv/
2. **Visual Studio Community** (free) - Download from: https://visualstudio.microsoft.com/
3. **CMake** - Download from: https://cmake.org/download/

## Installation Steps

### Step 1: Install ScriptHookV
1. Download ScriptHookV v3586.0 from the official website
2. Extract the contents to your GTA V directory:
   ```
   C:\Program Files (x86)\Steam\steamapps\common\Grand Theft Auto V\
   ```
3. You should have these files in your GTA V directory:
   - `ScriptHookV.dll`
   - `dinput8.dll` (ASI Loader)
   - `NativeTrainer.asi` (optional)

### Step 2: Verify Game Version
1. Check your `version.txt` file in the GTA V directory
2. Ensure it shows: `*********` or compatible
3. Check `versioninfo.txt` for: `GTA5.exe 1.0.3586.0`

### Step 3: Build the Mods
1. Open Command Prompt or PowerShell as Administrator
2. Navigate to the mod directory:
   ```cmd
   cd "C:\path\to\GTA5_Offline_Mods"
   ```
3. Create build directory:
   ```cmd
   mkdir build
   cd build
   ```
4. Generate build files:
   ```cmd
   cmake ..
   ```
5. Build the project:
   ```cmd
   cmake --build . --config Release
   ```

### Step 4: Install the Mods
1. Copy the built DLL to your GTA V directory:
   ```cmd
   copy bin\GTA5_Mods_DLL.dll "C:\Program Files (x86)\Steam\steamapps\common\Grand Theft Auto V\GTA5_Mods.asi"
   ```
   Note: Rename to `.asi` extension for ScriptHookV compatibility

## Usage Instructions

### Starting the Game
1. **IMPORTANT**: Ensure you're in Story Mode (offline)
2. Start GTA V normally through Steam or Rockstar Games Launcher
3. The mods will automatically load when you enter the game
4. You should see console messages indicating successful initialization

### Hotkey Controls
- **F1** - Show help menu
- **F2** - Toggle God Mode (invincibility)
- **F3** - Toggle Infinite Ammo
- **F4** - Toggle Super Speed (2x movement speed)
- **F5** - Toggle Vehicle God Mode
- **F6** - Repair current vehicle instantly
- **F7** - Add $100,000 to wallet
- **F8** - Clear wanted level
- **F9** - Teleport to Los Santos Airport
- **F10** - Cycle through weather types
- **F11** - Set time to noon
- **F12** - Disable all mods (emergency)

### Safety Features
- **Automatic Online Detection**: Mods automatically disable if online mode is detected
- **Memory Validation**: All memory operations are validated before execution
- **Safe Defaults**: Conservative settings to prevent game crashes
- **Emergency Disable**: F12 key instantly disables all active mods

## Troubleshooting

### Common Issues

#### "Mods not loading"
- Verify ScriptHookV is properly installed
- Check that the .asi file is in the correct directory
- Ensure GTA V version matches (1.0.3586.0)
- Run GTA V as Administrator

#### "Memory access errors"
- Update to the latest ScriptHookV version
- Verify game version compatibility
- Restart the game and try again

#### "Hotkeys not working"
- Make sure GTA V window has focus
- Try running as Administrator
- Check for conflicting key bindings

#### "Game crashes"
- Disable all mods (F12) immediately
- Verify game file integrity through Steam
- Update graphics drivers
- Check Windows Event Viewer for error details

### Debug Mode
For troubleshooting, you can run the console version:
```cmd
cd build\bin
GTA5_Mods_Console.exe
```
This will show detailed debug information.

## Advanced Configuration

### Custom Hotkeys
Edit the source code in `src/main.cpp` to change hotkey assignments:
```cpp
// Change F2 to F5 for God Mode
if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F5)) {
    BasicMods::Player::GodMode::Toggle();
}
```

### Speed Multipliers
Modify super speed multiplier in `src/BasicMods.cpp`:
```cpp
// Change from 2.0x to 3.0x speed
void SuperSpeed::Enable(float multiplier = 3.0f) {
```

### Money Amounts
Change money increment in `src/main.cpp`:
```cpp
// Change from $100,000 to $1,000,000
BasicMods::Player::MoneyMod::AddMoney(1000000);
```

## Uninstallation

1. Delete the `.asi` file from your GTA V directory:
   ```cmd
   del "C:\Program Files (x86)\Steam\steamapps\common\Grand Theft Auto V\GTA5_Mods.asi"
   ```
2. Optionally remove ScriptHookV files:
   - `ScriptHookV.dll`
   - `dinput8.dll`
   - `NativeTrainer.asi`

## Legal Disclaimer

- These mods are for **OFFLINE/SINGLE-PLAYER USE ONLY**
- Do not use in GTA Online - this will result in a ban
- The developers are not responsible for any issues or bans
- Use at your own risk
- Always backup your save files before using mods

## Support

For issues or questions:
1. Check this troubleshooting guide first
2. Verify your game version and ScriptHookV compatibility
3. Test with a clean game installation
4. Report bugs with detailed system information

## Version Compatibility

| GTA V Version | ScriptHookV Version | Mod Compatibility |
|---------------|-------------------|-------------------|
| 1.0.3586.0    | v3586.0          | ✅ Fully Compatible |
| 1.0.3274.0    | v3274.0          | ⚠️ May need updates |
| Older         | Older            | ❌ Not supported    |

Always ensure your GTA V version matches the supported version for best results.
