@echo off
echo ===================================
echo GTA V Offline Mods - Build Script
echo ===================================
echo.

REM Check if CMake is available
cmake --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: CMake not found! Please install CMake and add it to PATH.
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

echo [INFO] Generating build files...
cmake .. -G "Visual Studio 17 2022" -A x64
if errorlevel 1 (
    echo ERROR: Failed to generate build files!
    echo Make sure Visual Studio 2022 is installed.
    pause
    exit /b 1
)

echo [INFO] Building project...
cmake --build . --config Release
if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo ===================================
echo Build completed successfully!
echo ===================================
echo.
echo Output files:
echo - Console version: build\bin\Release\GTA5_Mods_Console.exe
echo - DLL version: build\bin\Release\GTA5_Mods_DLL.dll
echo.
echo Next steps:
echo 1. Install ScriptHookV in your GTA V directory
echo 2. Copy GTA5_Mods_DLL.dll to GTA V directory as GTA5_Mods.asi
echo 3. Start GTA V in Story Mode
echo.
echo See INSTALLATION_GUIDE.md for detailed instructions.
echo.
pause
