#include "BasicMods.h"
#include "MemoryUtils.h"
#include <iostream>
#include <thread>
#include <chrono>

namespace BasicMods {
    
    // Global safety flag
    static bool g_modsInitialized = false;
    static bool g_gameSafe = false;
    
    // Initialize all mods
    bool InitializeMods() {
        std::cout << "[INFO] Initializing GTA V Offline Mods..." << std::endl;
        
        // Initialize memory offsets first
        if (!MemoryUtils::InitializeOffsets()) {
            std::cout << "[ERROR] Failed to initialize memory offsets!" << std::endl;
            return false;
        }
        
        // Perform safety checks
        if (!IsGameSafe()) {
            std::cout << "[ERROR] Game safety check failed!" << std::endl;
            return false;
        }
        
        // Initialize hotkeys
        Hotkeys::InitializeHotkeys();
        Hotkeys::SetupDefaultHotkeys();
        
        g_modsInitialized = true;
        std::cout << "[INFO] GTA V Offline Mods initialized successfully!" << std::endl;
        std::cout << "[INFO] Press F1 for help menu" << std::endl;
        
        return true;
    }
    
    // Check if the game is safe for modding
    bool IsGameSafe() {
        // Check if we're in online mode
        if (MemoryUtils::IsOnlineMode()) {
            std::cout << "[WARNING] Online mode detected! Mods disabled for safety." << std::endl;
            g_gameSafe = false;
            return false;
        }
        
        // Check if local player exists
        void* player = MemoryUtils::GetLocalPlayer();
        if (!player) {
            std::cout << "[WARNING] Local player not found! Game may not be ready." << std::endl;
            g_gameSafe = false;
            return false;
        }
        
        g_gameSafe = true;
        return true;
    }
    
    // Continuous safety check
    void SafetyCheck() {
        static auto lastCheck = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        
        // Check every 5 seconds
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastCheck).count() >= 5) {
            if (MemoryUtils::IsOnlineMode()) {
                std::cout << "[SAFETY] Online mode detected! Disabling all mods..." << std::endl;
                
                // Disable all mods
                Player::GodMode::Disable();
                Player::InfiniteAmmo::Disable();
                Player::SuperSpeed::Disable();
                Vehicle::VehicleGodMode::Disable();
                Vehicle::FlyingCars::Disable();
                
                g_gameSafe = false;
            } else {
                g_gameSafe = true;
            }
            
            lastCheck = now;
        }
    }
    
    // Player Mods Implementation
    namespace Player {
        
        // God Mode Implementation
        bool GodMode::m_enabled = false;
        float GodMode::m_originalHealth = 0.0f;
        
        void GodMode::Enable() {
            if (!g_gameSafe) return;
            
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;
            
            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            
            // Store original health
            if (!m_enabled) {
                m_originalHealth = *reinterpret_cast<float*>(playerAddr + GTA5Offsets::Player::HEALTH);
            }
            
            // Set invincibility flag
            *reinterpret_cast<uint8_t*>(playerAddr + GTA5Offsets::Player::INVINCIBLE) = 1;
            
            // Set health to maximum
            *reinterpret_cast<float*>(playerAddr + GTA5Offsets::Player::HEALTH) = 
                *reinterpret_cast<float*>(playerAddr + GTA5Offsets::Player::MAX_HEALTH);
            
            m_enabled = true;
            std::cout << "[INFO] God Mode enabled" << std::endl;
        }
        
        void GodMode::Disable() {
            if (!m_enabled) return;
            
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;
            
            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            
            // Remove invincibility flag
            *reinterpret_cast<uint8_t*>(playerAddr + GTA5Offsets::Player::INVINCIBLE) = 0;
            
            // Restore original health if valid
            if (m_originalHealth > 0) {
                *reinterpret_cast<float*>(playerAddr + GTA5Offsets::Player::HEALTH) = m_originalHealth;
            }
            
            m_enabled = false;
            std::cout << "[INFO] God Mode disabled" << std::endl;
        }
        
        void GodMode::Toggle() {
            if (m_enabled) {
                Disable();
            } else {
                Enable();
            }
        }
        
        void GodMode::Update() {
            if (!m_enabled || !g_gameSafe) return;
            
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;
            
            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            
            // Maintain maximum health
            float maxHealth = *reinterpret_cast<float*>(playerAddr + GTA5Offsets::Player::MAX_HEALTH);
            *reinterpret_cast<float*>(playerAddr + GTA5Offsets::Player::HEALTH) = maxHealth;
            
            // Maintain invincibility flag
            *reinterpret_cast<uint8_t*>(playerAddr + GTA5Offsets::Player::INVINCIBLE) = 1;
        }
        
        // Infinite Ammo Implementation
        bool InfiniteAmmo::m_enabled = false;
        
        void InfiniteAmmo::Enable() {
            if (!g_gameSafe) return;
            
            m_enabled = true;
            std::cout << "[INFO] Infinite Ammo enabled" << std::endl;
        }
        
        void InfiniteAmmo::Disable() {
            m_enabled = false;
            std::cout << "[INFO] Infinite Ammo disabled" << std::endl;
        }
        
        void InfiniteAmmo::Toggle() {
            if (m_enabled) {
                Disable();
            } else {
                Enable();
            }
        }
        
        void InfiniteAmmo::Update() {
            if (!m_enabled || !g_gameSafe) return;
            
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;
            
            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            uintptr_t playerInfo = *reinterpret_cast<uintptr_t*>(playerAddr + GTA5Offsets::Player::PLAYER_INFO);
            
            if (!MemoryUtils::IsValidPointer(playerInfo)) return;
            
            uintptr_t weaponManager = *reinterpret_cast<uintptr_t*>(playerInfo + GTA5Offsets::PlayerInfo::WEAPON_MANAGER);
            if (!MemoryUtils::IsValidPointer(weaponManager)) return;
            
            // Set infinite ammo flag (simplified implementation)
            *reinterpret_cast<uint8_t*>(weaponManager + GTA5Offsets::Weapon::INFINITE_AMMO) = 1;
        }
        
        // Super Speed Implementation
        bool SuperSpeed::m_enabled = false;
        float SuperSpeed::m_speedMultiplier = 2.0f;
        float SuperSpeed::m_originalRunSpeed = 0.0f;
        float SuperSpeed::m_originalSwimSpeed = 0.0f;
        
        void SuperSpeed::Enable(float multiplier) {
            if (!g_gameSafe) return;
            
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;
            
            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            uintptr_t playerInfo = *reinterpret_cast<uintptr_t*>(playerAddr + GTA5Offsets::Player::PLAYER_INFO);
            
            if (!MemoryUtils::IsValidPointer(playerInfo)) return;
            
            // Store original speeds
            if (!m_enabled) {
                m_originalRunSpeed = *reinterpret_cast<float*>(playerInfo + GTA5Offsets::PlayerInfo::RUN_SPEED);
                m_originalSwimSpeed = *reinterpret_cast<float*>(playerInfo + GTA5Offsets::PlayerInfo::SWIM_SPEED);
            }
            
            m_speedMultiplier = multiplier;
            
            // Apply speed multiplier
            *reinterpret_cast<float*>(playerInfo + GTA5Offsets::PlayerInfo::RUN_SPEED) = m_originalRunSpeed * multiplier;
            *reinterpret_cast<float*>(playerInfo + GTA5Offsets::PlayerInfo::SWIM_SPEED) = m_originalSwimSpeed * multiplier;
            
            m_enabled = true;
            std::cout << "[INFO] Super Speed enabled (x" << multiplier << ")" << std::endl;
        }
        
        void SuperSpeed::Disable() {
            if (!m_enabled) return;
            
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;
            
            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            uintptr_t playerInfo = *reinterpret_cast<uintptr_t*>(playerAddr + GTA5Offsets::Player::PLAYER_INFO);
            
            if (!MemoryUtils::IsValidPointer(playerInfo)) return;
            
            // Restore original speeds
            *reinterpret_cast<float*>(playerInfo + GTA5Offsets::PlayerInfo::RUN_SPEED) = m_originalRunSpeed;
            *reinterpret_cast<float*>(playerInfo + GTA5Offsets::PlayerInfo::SWIM_SPEED) = m_originalSwimSpeed;
            
            m_enabled = false;
            std::cout << "[INFO] Super Speed disabled" << std::endl;
        }
        
        void SuperSpeed::Toggle() {
            if (m_enabled) {
                Disable();
            } else {
                Enable();
            }
        }
        
        void SuperSpeed::SetSpeedMultiplier(float multiplier) {
            m_speedMultiplier = multiplier;
            if (m_enabled) {
                Enable(multiplier); // Re-enable with new multiplier
            }
        }

        // Money Mod Implementation
        int32_t MoneyMod::GetMoney() {
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return 0;

            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            uintptr_t playerInfo = *reinterpret_cast<uintptr_t*>(playerAddr + GTA5Offsets::Player::PLAYER_INFO);

            if (!MemoryUtils::IsValidPointer(playerInfo)) return 0;

            return *reinterpret_cast<int32_t*>(playerInfo + GTA5Offsets::PlayerInfo::MONEY);
        }

        void MoneyMod::SetMoney(int32_t amount) {
            if (!g_gameSafe || !IsValidAmount(amount)) return;

            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;

            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            uintptr_t playerInfo = *reinterpret_cast<uintptr_t*>(playerAddr + GTA5Offsets::Player::PLAYER_INFO);

            if (!MemoryUtils::IsValidPointer(playerInfo)) return;

            *reinterpret_cast<int32_t*>(playerInfo + GTA5Offsets::PlayerInfo::MONEY) = amount;
            std::cout << "[INFO] Money set to $" << amount << std::endl;
        }

        void MoneyMod::AddMoney(int32_t amount) {
            int32_t currentMoney = GetMoney();
            int32_t newAmount = currentMoney + amount;

            if (IsValidAmount(newAmount)) {
                SetMoney(newAmount);
            }
        }

        bool MoneyMod::IsValidAmount(int32_t amount) {
            // Reasonable limits to avoid game issues
            return amount >= 0 && amount <= 2000000000; // 2 billion max
        }

        // Teleport Implementation
        bool Teleport::TeleportTo(float x, float y, float z) {
            return TeleportTo(GTA5Offsets::Vec3(x, y, z));
        }

        bool Teleport::TeleportTo(const GTA5Offsets::Vec3& position) {
            if (!g_gameSafe) return false;

            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return false;

            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);

            // Set position
            *reinterpret_cast<GTA5Offsets::Vec3*>(playerAddr + GTA5Offsets::Player::POSITION) = position;

            // Clear velocity to prevent momentum issues
            GTA5Offsets::Vec3 zeroVelocity(0, 0, 0);
            *reinterpret_cast<GTA5Offsets::Vec3*>(playerAddr + GTA5Offsets::Player::VELOCITY) = zeroVelocity;

            std::cout << "[INFO] Teleported to (" << position.x << ", " << position.y << ", " << position.z << ")" << std::endl;
            return true;
        }

        GTA5Offsets::Vec3 Teleport::GetPosition() {
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return GTA5Offsets::Vec3();

            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            return *reinterpret_cast<GTA5Offsets::Vec3*>(playerAddr + GTA5Offsets::Player::POSITION);
        }

        bool Teleport::SetPosition(const GTA5Offsets::Vec3& position) {
            return TeleportTo(position);
        }

        // Predefined teleport locations
        void Teleport::TeleportToAirport() {
            TeleportTo(-1336.0f, -3044.0f, 14.0f); // Los Santos International Airport
        }

        void Teleport::TeleportToDowntown() {
            TeleportTo(-228.0f, -618.0f, 33.0f); // Downtown Los Santos
        }

        void Teleport::TeleportToBeach() {
            TeleportTo(-1850.0f, -1231.0f, 13.0f); // Vespucci Beach
        }

        void Teleport::TeleportToMountain() {
            TeleportTo(2868.0f, 5994.0f, 358.0f); // Mount Chiliad
        }

        // Wanted Level Implementation
        int WantedLevel::GetWantedLevel() {
            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return 0;

            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            return *reinterpret_cast<int*>(playerAddr + GTA5Offsets::Player::WANTED_LEVEL);
        }

        void WantedLevel::SetWantedLevel(int level) {
            if (!g_gameSafe) return;

            // Clamp level between 0 and 5
            level = max(0, min(5, level));

            void* player = MemoryUtils::GetLocalPlayer();
            if (!player) return;

            uintptr_t playerAddr = reinterpret_cast<uintptr_t>(player);
            *reinterpret_cast<int*>(playerAddr + GTA5Offsets::Player::WANTED_LEVEL) = level;

            std::cout << "[INFO] Wanted level set to " << level << std::endl;
        }

        void WantedLevel::ClearWantedLevel() {
            SetWantedLevel(0);
        }

        void WantedLevel::MaxWantedLevel() {
            SetWantedLevel(5);
        }
    }

    // Vehicle Mods Implementation
    namespace Vehicle {

        // Vehicle God Mode Implementation
        bool VehicleGodMode::m_enabled = false;

        void VehicleGodMode::Enable() {
            if (!g_gameSafe) return;

            m_enabled = true;
            std::cout << "[INFO] Vehicle God Mode enabled" << std::endl;
        }

        void VehicleGodMode::Disable() {
            m_enabled = false;
            std::cout << "[INFO] Vehicle God Mode disabled" << std::endl;
        }

        void VehicleGodMode::Toggle() {
            if (m_enabled) {
                Disable();
            } else {
                Enable();
            }
        }

        void VehicleGodMode::Update() {
            if (!m_enabled || !g_gameSafe) return;

            void* vehicle = MemoryUtils::GetPlayerVehicle();
            if (!vehicle) return;

            uintptr_t vehicleAddr = reinterpret_cast<uintptr_t>(vehicle);

            // Set invincibility flag
            *reinterpret_cast<uint8_t*>(vehicleAddr + GTA5Offsets::Vehicle::INVINCIBLE) = 1;

            // Maintain maximum health
            float maxHealth = *reinterpret_cast<float*>(vehicleAddr + GTA5Offsets::Vehicle::MAX_HEALTH);
            *reinterpret_cast<float*>(vehicleAddr + GTA5Offsets::Vehicle::HEALTH) = maxHealth;
            *reinterpret_cast<float*>(vehicleAddr + GTA5Offsets::Vehicle::ENGINE_HEALTH) = 1000.0f;
            *reinterpret_cast<float*>(vehicleAddr + GTA5Offsets::Vehicle::PETROL_TANK_HEALTH) = 1000.0f;

            // Clean the vehicle
            *reinterpret_cast<float*>(vehicleAddr + GTA5Offsets::Vehicle::DIRT_LEVEL) = 0.0f;
        }
    }
}
