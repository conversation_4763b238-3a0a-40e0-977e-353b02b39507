#pragma once
#include "MemoryOffsets.h"
#include <Windows.h>

// Basic GTA V Offline Mods
// All mods include safety checks to ensure offline-only operation

namespace BasicMods {
    
    // Safety and initialization
    bool InitializeMods();
    bool IsGameSafe();
    void SafetyCheck();
    
    // Player modifications
    namespace Player {
        
        // God mode - makes player invincible
        class GodMode {
        private:
            static bool m_enabled;
            static float m_originalHealth;
            
        public:
            static bool IsEnabled() { return m_enabled; }
            static void Enable();
            static void Disable();
            static void Toggle();
            static void Update(); // Call this regularly to maintain god mode
        };
        
        // Infinite ammo
        class InfiniteAmmo {
        private:
            static bool m_enabled;
            
        public:
            static bool IsEnabled() { return m_enabled; }
            static void Enable();
            static void Disable();
            static void Toggle();
            static void Update();
        };
        
        // Super speed
        class SuperSpeed {
        private:
            static bool m_enabled;
            static float m_speedMultiplier;
            static float m_originalRunSpeed;
            static float m_originalSwimSpeed;
            
        public:
            static bool IsEnabled() { return m_enabled; }
            static void Enable(float multiplier = 2.0f);
            static void Disable();
            static void Toggle();
            static void SetSpeedMultiplier(float multiplier);
            static float GetSpeedMultiplier() { return m_speedMultiplier; }
        };
        
        // Money modifier
        class MoneyMod {
        public:
            static int32_t GetMoney();
            static void SetMoney(int32_t amount);
            static void AddMoney(int32_t amount);
            static bool IsValidAmount(int32_t amount);
        };
        
        // Teleportation
        class Teleport {
        public:
            static bool TeleportTo(float x, float y, float z);
            static bool TeleportTo(const GTA5Offsets::Vec3& position);
            static GTA5Offsets::Vec3 GetPosition();
            static bool SetPosition(const GTA5Offsets::Vec3& position);
            
            // Predefined locations
            static void TeleportToAirport();
            static void TeleportToDowntown();
            static void TeleportToBeach();
            static void TeleportToMountain();
        };
        
        // Wanted level control
        class WantedLevel {
        public:
            static int GetWantedLevel();
            static void SetWantedLevel(int level);
            static void ClearWantedLevel();
            static void MaxWantedLevel();
        };
    }
    
    // Vehicle modifications
    namespace Vehicle {
        
        // Vehicle god mode
        class VehicleGodMode {
        private:
            static bool m_enabled;
            
        public:
            static bool IsEnabled() { return m_enabled; }
            static void Enable();
            static void Disable();
            static void Toggle();
            static void Update();
        };
        
        // Vehicle repair
        class VehicleRepair {
        public:
            static bool RepairCurrentVehicle();
            static bool RepairVehicle(void* vehicle);
            static void InstantRepair(); // Hotkey function
        };
        
        // Vehicle spawner
        class VehicleSpawner {
        public:
            // Common vehicle hashes
            enum VehicleHash : uint32_t {
                ADDER = 0xB779A091,
                ZENTORNO = 0xAC5DF515,
                INSURGENT = 0x9114EADA,
                RHINO = 0x782665D7,
                BUZZARD = 0x2F03547B,
                HYDRA = 0x39D6E83F,
                OPPRESSOR = 0x34B82784,
                DELUXO = 0x586765FB
            };
            
            static bool SpawnVehicle(uint32_t vehicleHash);
            static bool SpawnVehicle(uint32_t vehicleHash, const GTA5Offsets::Vec3& position);
            static void SpawnRandomSupercar();
            static void SpawnRandomHelicopter();
            static void SpawnTank();
        };
        
        // Flying cars
        class FlyingCars {
        private:
            static bool m_enabled;
            
        public:
            static bool IsEnabled() { return m_enabled; }
            static void Enable();
            static void Disable();
            static void Toggle();
            static void Update();
        };
    }
    
    // World modifications
    namespace World {
        
        // Weather control
        class WeatherControl {
        private:
            static GTA5Offsets::WeatherType m_currentWeather;
            
        public:
            static void SetWeather(GTA5Offsets::WeatherType weather);
            static GTA5Offsets::WeatherType GetWeather();
            static void ClearWeather();
            static void RainWeather();
            static void StormWeather();
            static void SnowWeather();
            static void CycleWeather();
        };
        
        // Time control
        class TimeControl {
        public:
            static void SetTime(int hour, int minute);
            static void SetTimeScale(float scale);
            static void PauseTime();
            static void ResumeTime();
            static void FastForward();
            static void SlowMotion();
            
            // Quick time sets
            static void SetMorning();   // 8:00 AM
            static void SetNoon();      // 12:00 PM
            static void SetEvening();   // 6:00 PM
            static void SetMidnight();  // 12:00 AM
        };
        
        // Gravity control
        class GravityControl {
        private:
            static float m_originalGravity;
            static float m_currentGravity;
            
        public:
            static void SetGravity(float gravity);
            static float GetGravity();
            static void ResetGravity();
            static void LowGravity();
            static void HighGravity();
            static void ZeroGravity();
        };
    }
    
    // Hotkey system
    namespace Hotkeys {
        
        // Hotkey definitions
        enum Key {
            F1 = VK_F1,
            F2 = VK_F2,
            F3 = VK_F3,
            F4 = VK_F4,
            F5 = VK_F5,
            F6 = VK_F6,
            F7 = VK_F7,
            F8 = VK_F8,
            F9 = VK_F9,
            F10 = VK_F10,
            F11 = VK_F11,
            F12 = VK_F12,
            NUM_1 = VK_NUMPAD1,
            NUM_2 = VK_NUMPAD2,
            NUM_3 = VK_NUMPAD3,
            NUM_4 = VK_NUMPAD4,
            NUM_5 = VK_NUMPAD5,
            NUM_6 = VK_NUMPAD6,
            NUM_7 = VK_NUMPAD7,
            NUM_8 = VK_NUMPAD8,
            NUM_9 = VK_NUMPAD9,
            NUM_0 = VK_NUMPAD0
        };
        
        void InitializeHotkeys();
        void ProcessHotkeys();
        bool IsKeyPressed(Key key);
        bool IsKeyJustPressed(Key key);
        
        // Default hotkey assignments
        void SetupDefaultHotkeys();
    }
}
