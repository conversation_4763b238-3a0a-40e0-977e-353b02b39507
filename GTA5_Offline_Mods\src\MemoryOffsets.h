#pragma once
#include <Windows.h>
#include <cstdint>

// GTA V Memory Offsets and Structures for Version 1.0.3586.0
// Compatible with ScriptHookV v3586.0

namespace GTA5Offsets {
    
    // Base addresses - these will be resolved at runtime using pattern scanning
    extern uintptr_t g_WorldPtr;
    extern uintptr_t g_LocalPlayerPtr;
    extern uintptr_t g_GlobalsPtr;
    
    // Memory patterns for dynamic resolution
    namespace Patterns {
        // World pointer pattern
        constexpr const char* WORLD_PTR = "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9 74 07";
        
        // Local player pattern  
        constexpr const char* LOCAL_PLAYER = "48 8B 0D ? ? ? ? 48 8B 01 FF 50 08 48 85 C0 74 0A";
        
        // Globals pattern
        constexpr const char* GLOBALS = "48 8D 15 ? ? ? ? 4C 8B C0 E8 ? ? ? ? 48 85 C0 75 0A";
    }
    
    // Player structure offsets
    namespace Player {
        constexpr uint32_t HEALTH = 0x280;           // Player health
        constexpr uint32_t MAX_HEALTH = 0x284;       // Maximum health
        constexpr uint32_t ARMOR = 0x14E0;           // Player armor
        constexpr uint32_t POSITION = 0x90;          // Vec3 position (X, Y, Z)
        constexpr uint32_t VELOCITY = 0xA0;          // Vec3 velocity
        constexpr uint32_t ROTATION = 0xB0;          // Vec3 rotation
        constexpr uint32_t PLAYER_INFO = 0x1078;     // PlayerInfo structure
        constexpr uint32_t INVINCIBLE = 0x188;       // Invincibility flag
        constexpr uint32_t WANTED_LEVEL = 0x888;     // Wanted level
    }
    
    // PlayerInfo structure offsets
    namespace PlayerInfo {
        constexpr uint32_t NAME = 0x6C;              // Player name (local only)
        constexpr uint32_t MONEY = 0x1000;           // Money amount
        constexpr uint32_t WEAPON_MANAGER = 0x10D8;  // Weapon manager pointer
        constexpr uint32_t RUN_SPEED = 0x1C8;        // Running speed multiplier
        constexpr uint32_t SWIM_SPEED = 0x1CC;       // Swimming speed multiplier
    }
    
    // Vehicle structure offsets
    namespace Vehicle {
        constexpr uint32_t HEALTH = 0x280;           // Vehicle health
        constexpr uint32_t MAX_HEALTH = 0x284;       // Maximum health
        constexpr uint32_t POSITION = 0x90;          // Vec3 position
        constexpr uint32_t VELOCITY = 0xA0;          // Vec3 velocity
        constexpr uint32_t ROTATION = 0xB0;          // Vec3 rotation
        constexpr uint32_t ENGINE_HEALTH = 0x908;    // Engine health
        constexpr uint32_t PETROL_TANK_HEALTH = 0x90C; // Fuel tank health
        constexpr uint32_t DIRT_LEVEL = 0x918;       // Vehicle dirt level
        constexpr uint32_t INVINCIBLE = 0x188;       // Vehicle invincibility
        constexpr uint32_t GRAVITY = 0xBCC;          // Gravity multiplier
    }
    
    // Weapon structure offsets
    namespace Weapon {
        constexpr uint32_t AMMO_COUNT = 0x18;        // Current ammo count
        constexpr uint32_t MAX_AMMO = 0x1C;          // Maximum ammo
        constexpr uint32_t WEAPON_HASH = 0x10;       // Weapon hash identifier
        constexpr uint32_t INFINITE_AMMO = 0x20;     // Infinite ammo flag
    }
    
    // World/Global offsets
    namespace World {
        constexpr uint32_t WEATHER = 0x1000;         // Weather type
        constexpr uint32_t TIME_HOUR = 0x1004;       // Current hour
        constexpr uint32_t TIME_MINUTE = 0x1008;     // Current minute
        constexpr uint32_t TIME_SCALE = 0x100C;      // Time scale multiplier
        constexpr uint32_t GRAVITY = 0x1010;         // World gravity
    }
    
    // Common data structures
    struct Vec3 {
        float x, y, z;
        Vec3() : x(0), y(0), z(0) {}
        Vec3(float x, float y, float z) : x(x), y(y), z(z) {}
    };
    
    struct Vec4 {
        float x, y, z, w;
        Vec4() : x(0), y(0), z(0), w(0) {}
        Vec4(float x, float y, float z, float w) : x(x), y(y), z(z), w(w) {}
    };
    
    // Entity types
    enum class EntityType : uint8_t {
        PLAYER = 1,
        VEHICLE = 2,
        OBJECT = 3,
        PED = 4
    };
    
    // Weather types
    enum class WeatherType : uint32_t {
        CLEAR = 0,
        EXTRASUNNY = 1,
        CLOUDS = 2,
        OVERCAST = 3,
        RAIN = 4,
        DRIZZLE = 5,
        THUNDER = 6,
        CLEARING = 7,
        NEUTRAL = 8,
        SNOW = 9,
        BLIZZARD = 10,
        SNOWLIGHT = 11,
        XMAS = 12,
        HALLOWEEN = 13
    };
}

// Function declarations
namespace MemoryUtils {
    bool InitializeOffsets();
    uintptr_t FindPattern(const char* pattern, const char* mask = nullptr);
    bool IsValidPointer(uintptr_t ptr);
    bool IsOnlineMode();
    void* GetLocalPlayer();
    void* GetPlayerVehicle();
}
