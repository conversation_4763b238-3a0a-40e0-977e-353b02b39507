#include "MemoryOffsets.h"
#include <iostream>
#include <vector>
#include <TlHelp32.h>

namespace GTA5Offsets {
    uintptr_t g_WorldPtr = 0;
    uintptr_t g_LocalPlayerPtr = 0;
    uintptr_t g_GlobalsPtr = 0;
}

namespace MemoryUtils {
    
    // Get the base address of GTA5.exe
    uintptr_t GetModuleBaseAddress(const wchar_t* moduleName) {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, GetCurrentProcessId());
        if (hSnapshot == INVALID_HANDLE_VALUE) return 0;
        
        MODULEENTRY32W moduleEntry;
        moduleEntry.dwSize = sizeof(MODULEENTRY32W);
        
        if (Module32FirstW(hSnapshot, &moduleEntry)) {
            do {
                if (wcscmp(moduleEntry.szModule, moduleName) == 0) {
                    CloseHandle(hSnapshot);
                    return reinterpret_cast<uintptr_t>(moduleEntry.modBaseAddr);
                }
            } while (Module32NextW(hSnapshot, &moduleEntry));
        }
        
        CloseHandle(hSnapshot);
        return 0;
    }
    
    // Pattern scanning function
    uintptr_t FindPattern(const char* pattern, const char* mask) {
        static uintptr_t baseAddress = GetModuleBaseAddress(L"GTA5.exe");
        if (!baseAddress) return 0;
        
        MEMORY_BASIC_INFORMATION mbi;
        uintptr_t currentAddress = baseAddress;
        
        while (VirtualQuery(reinterpret_cast<LPCVOID>(currentAddress), &mbi, sizeof(mbi))) {
            if (mbi.State == MEM_COMMIT && (mbi.Protect & PAGE_GUARD) == 0) {
                for (uintptr_t i = 0; i < mbi.RegionSize; i++) {
                    bool found = true;
                    for (size_t j = 0; pattern[j] != '\0'; j++) {
                        if (mask && mask[j] == '?') continue;
                        if (pattern[j] != *reinterpret_cast<char*>(currentAddress + i + j)) {
                            found = false;
                            break;
                        }
                    }
                    if (found) return currentAddress + i;
                }
            }
            currentAddress += mbi.RegionSize;
        }
        return 0;
    }
    
    // Simplified pattern scanning for IDA-style patterns
    uintptr_t FindPatternIDA(const char* pattern) {
        static uintptr_t baseAddress = GetModuleBaseAddress(L"GTA5.exe");
        if (!baseAddress) return 0;
        
        std::vector<int> patternBytes;
        std::vector<bool> mask;
        
        // Parse IDA-style pattern (e.g., "48 8B 05 ? ? ? ?")
        const char* current = pattern;
        while (*current) {
            if (*current == ' ') {
                current++;
                continue;
            }
            
            if (*current == '?') {
                patternBytes.push_back(0);
                mask.push_back(false);
                current++;
            } else {
                char hex[3] = {current[0], current[1], '\0'};
                patternBytes.push_back(static_cast<int>(strtol(hex, nullptr, 16)));
                mask.push_back(true);
                current += 2;
            }
        }
        
        MEMORY_BASIC_INFORMATION mbi;
        uintptr_t currentAddress = baseAddress;
        
        while (VirtualQuery(reinterpret_cast<LPCVOID>(currentAddress), &mbi, sizeof(mbi))) {
            if (mbi.State == MEM_COMMIT && (mbi.Protect & PAGE_GUARD) == 0 && 
                (mbi.Protect & PAGE_EXECUTE_READ) == PAGE_EXECUTE_READ) {
                
                for (uintptr_t i = 0; i < mbi.RegionSize - patternBytes.size(); i++) {
                    bool found = true;
                    for (size_t j = 0; j < patternBytes.size(); j++) {
                        if (!mask[j]) continue;
                        if (patternBytes[j] != *reinterpret_cast<uint8_t*>(currentAddress + i + j)) {
                            found = false;
                            break;
                        }
                    }
                    if (found) return currentAddress + i;
                }
            }
            currentAddress += mbi.RegionSize;
        }
        return 0;
    }
    
    // Initialize all memory offsets using pattern scanning
    bool InitializeOffsets() {
        std::cout << "[INFO] Initializing GTA V memory offsets..." << std::endl;
        
        // Find World pointer
        uintptr_t worldPattern = FindPatternIDA(GTA5Offsets::Patterns::WORLD_PTR);
        if (worldPattern) {
            // Extract the relative offset and calculate absolute address
            int32_t offset = *reinterpret_cast<int32_t*>(worldPattern + 3);
            GTA5Offsets::g_WorldPtr = worldPattern + 7 + offset;
            std::cout << "[INFO] World pointer found at: 0x" << std::hex << GTA5Offsets::g_WorldPtr << std::endl;
        } else {
            std::cout << "[ERROR] Failed to find World pointer!" << std::endl;
            return false;
        }
        
        // Find Local Player pointer
        uintptr_t playerPattern = FindPatternIDA(GTA5Offsets::Patterns::LOCAL_PLAYER);
        if (playerPattern) {
            int32_t offset = *reinterpret_cast<int32_t*>(playerPattern + 3);
            GTA5Offsets::g_LocalPlayerPtr = playerPattern + 7 + offset;
            std::cout << "[INFO] Local Player pointer found at: 0x" << std::hex << GTA5Offsets::g_LocalPlayerPtr << std::endl;
        } else {
            std::cout << "[ERROR] Failed to find Local Player pointer!" << std::endl;
            return false;
        }
        
        // Find Globals pointer
        uintptr_t globalsPattern = FindPatternIDA(GTA5Offsets::Patterns::GLOBALS);
        if (globalsPattern) {
            int32_t offset = *reinterpret_cast<int32_t*>(globalsPattern + 3);
            GTA5Offsets::g_GlobalsPtr = globalsPattern + 7 + offset;
            std::cout << "[INFO] Globals pointer found at: 0x" << std::hex << GTA5Offsets::g_GlobalsPtr << std::endl;
        } else {
            std::cout << "[ERROR] Failed to find Globals pointer!" << std::endl;
            return false;
        }
        
        std::cout << "[INFO] Memory offsets initialized successfully!" << std::endl;
        return true;
    }
    
    // Check if a pointer is valid
    bool IsValidPointer(uintptr_t ptr) {
        if (ptr == 0) return false;
        
        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(reinterpret_cast<LPCVOID>(ptr), &mbi, sizeof(mbi)) == 0) {
            return false;
        }
        
        return (mbi.State == MEM_COMMIT) && 
               (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
    }
    
    // Check if the game is in online mode
    bool IsOnlineMode() {
        // This is a simplified check - in a real implementation, you'd check specific game state
        // For safety, we'll assume offline mode for now
        return false;
    }
    
    // Get local player entity
    void* GetLocalPlayer() {
        if (!IsValidPointer(GTA5Offsets::g_LocalPlayerPtr)) return nullptr;
        
        uintptr_t worldPtr = *reinterpret_cast<uintptr_t*>(GTA5Offsets::g_WorldPtr);
        if (!IsValidPointer(worldPtr)) return nullptr;
        
        uintptr_t localPlayerPtr = *reinterpret_cast<uintptr_t*>(worldPtr + 0x08);
        return reinterpret_cast<void*>(localPlayerPtr);
    }
    
    // Get player's current vehicle
    void* GetPlayerVehicle() {
        void* player = GetLocalPlayer();
        if (!player) return nullptr;
        
        // Check if player is in a vehicle (simplified)
        uintptr_t vehiclePtr = *reinterpret_cast<uintptr_t*>(reinterpret_cast<uintptr_t>(player) + 0xD30);
        if (!IsValidPointer(vehiclePtr)) return nullptr;
        
        return reinterpret_cast<void*>(vehiclePtr);
    }
}
