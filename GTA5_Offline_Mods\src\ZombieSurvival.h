#pragma once
#include "MemoryOffsets.h"
#include "BasicMods.h"
#include <vector>
#include <chrono>
#include <random>

// GTA V Zombie Horde Survival System
// Transforms Los Santos into a zombie apocalypse survival experience

namespace ZombieSurvival {
    
    // Forward declarations
    struct Zombie;
    struct SafeZone;
    struct SurvivalStats;
    
    // Zombie types with different behaviors and abilities
    enum class ZombieType {
        WALKER,      // Slow, basic zombie
        RUNNER,      // Fast, aggressive zombie  
        CRAWLER,     // Low profile, hard to spot
        BRUTE,       // High health, strong attacks
        SPITTER,     // Ranged acid attacks
        SCREAMER,    // Attracts other zombies
        BOSS         // Special boss zombies
    };
    
    // Zombie behavior states
    enum class ZombieState {
        IDLE,        // Standing around
        WANDERING,   // Random movement
        HUNTING,     // Searching for player
        CHASING,     // Actively pursuing player
        ATTACKING,   // In combat with player
        FEEDING,     // Eating corpse (distracted)
        DEAD         // Killed by player
    };
    
    // Survival game states
    enum class GameState {
        INACTIVE,    // Survival mode not started
        PREPARING,   // Grace period before first wave
        WAVE_ACTIVE, // Zombies are spawning/active
        WAVE_BREAK,  // Brief rest between waves
        GAME_OVER,   // Player died
        VICTORY      // Survived all waves
    };
    
    // Individual zombie data structure
    struct Zombie {
        void* npcPtr;                    // Pointer to NPC entity
        ZombieType type;                 // Zombie variant
        ZombieState state;               // Current behavior
        float health;                    // Current health
        float maxHealth;                 // Maximum health
        float speed;                     // Movement speed multiplier
        float damage;                    // Attack damage
        float detectionRange;            // How far they can see player
        float attackRange;               // Melee attack range
        GTA5Offsets::Vec3 lastKnownPlayerPos; // Last seen player position
        std::chrono::steady_clock::time_point lastAttack; // Attack cooldown
        std::chrono::steady_clock::time_point spawnTime;  // When spawned
        bool isConverted;                // Was this an existing NPC?
        uint32_t waveNumber;             // Which wave spawned this zombie
        
        Zombie() : npcPtr(nullptr), type(ZombieType::WALKER), state(ZombieState::IDLE),
                  health(100.0f), maxHealth(100.0f), speed(1.0f), damage(25.0f),
                  detectionRange(50.0f), attackRange(2.0f), isConverted(false), waveNumber(0) {}
    };
    
    // Safe zone locations where player can fortify
    struct SafeZone {
        GTA5Offsets::Vec3 center;        // Center position
        float radius;                    // Safe zone radius
        std::string name;                // Location name
        bool isActive;                   // Currently fortified
        int barricadeLevel;              // Fortification strength (0-5)
        std::vector<GTA5Offsets::Vec3> barricadePositions; // Barricade locations
        std::chrono::steady_clock::time_point lastBreached; // When zombies broke in
        
        SafeZone(const GTA5Offsets::Vec3& pos, float r, const std::string& n) 
            : center(pos), radius(r), name(n), isActive(false), barricadeLevel(0) {}
    };
    
    // Player survival statistics
    struct SurvivalStats {
        int currentWave;                 // Current wave number
        int zombiesKilled;               // Total zombies eliminated
        int zombiesRemaining;            // Zombies left in current wave
        int totalZombiesSpawned;         // Total spawned this session
        float survivalTime;              // Time survived in seconds
        int headshotKills;               // Headshot kills
        int meleeKills;                  // Melee kills
        int explosiveKills;              // Explosive kills
        float accuracy;                  // Shooting accuracy percentage
        int barricadesBuilt;             // Barricades constructed
        int safezoneBreaches;            // Times safe zones were breached
        
        SurvivalStats() : currentWave(0), zombiesKilled(0), zombiesRemaining(0),
                         totalZombiesSpawned(0), survivalTime(0.0f), headshotKills(0),
                         meleeKills(0), explosiveKills(0), accuracy(0.0f),
                         barricadesBuilt(0), safezoneBreaches(0) {}
    };
    
    // Wave configuration
    struct WaveConfig {
        int waveNumber;                  // Wave identifier
        int zombieCount;                 // Total zombies to spawn
        float spawnRate;                 // Zombies per second
        float difficultyMultiplier;      // Health/damage multiplier
        std::vector<ZombieType> allowedTypes; // Zombie types for this wave
        float specialZombieChance;       // Chance for special zombies
        int duration;                    // Wave duration in seconds
        std::string description;         // Wave description
        
        WaveConfig(int wave, int count, float rate, float difficulty) 
            : waveNumber(wave), zombieCount(count), spawnRate(rate), 
              difficultyMultiplier(difficulty), specialZombieChance(0.1f), 
              duration(300) {} // 5 minutes default
    };
    
    // Main zombie survival system class
    class ZombieSurvivalSystem {
    private:
        static bool m_active;
        static GameState m_gameState;
        static std::vector<Zombie> m_zombies;
        static std::vector<SafeZone> m_safeZones;
        static SurvivalStats m_stats;
        static std::vector<WaveConfig> m_waveConfigs;
        static std::chrono::steady_clock::time_point m_waveStartTime;
        static std::chrono::steady_clock::time_point m_gameStartTime;
        static std::mt19937 m_randomEngine;
        
        // Internal helper methods
        static void InitializeWaveConfigs();
        static void InitializeSafeZones();
        static GTA5Offsets::Vec3 GetRandomSpawnPosition();
        static bool IsPositionSafe(const GTA5Offsets::Vec3& pos);
        static void* FindNearestNPC(const GTA5Offsets::Vec3& position, float maxDistance);
        static void ConvertNPCToZombie(void* npc, ZombieType type);
        static void UpdateZombieAI(Zombie& zombie);
        static void HandleZombieAttack(Zombie& zombie);
        static bool IsPlayerInSafeZone();
        static SafeZone* GetNearestSafeZone();
        
    public:
        // System control
        static bool IsActive() { return m_active; }
        static GameState GetGameState() { return m_gameState; }
        static const SurvivalStats& GetStats() { return m_stats; }
        
        static bool Initialize();
        static void Shutdown();
        static void StartSurvival();
        static void StopSurvival();
        static void Update();
        
        // Wave management
        static void StartNextWave();
        static void EndCurrentWave();
        static bool IsWaveComplete();
        static WaveConfig GetCurrentWaveConfig();
        
        // Zombie management
        static void SpawnZombie(const GTA5Offsets::Vec3& position, ZombieType type);
        static void SpawnZombieWave();
        static void RemoveDeadZombies();
        static void KillAllZombies();
        static int GetActiveZombieCount();
        static std::vector<Zombie*> GetNearbyZombies(const GTA5Offsets::Vec3& position, float radius);
        
        // Safe zone management
        static void FortifySafeZone();
        static void RepairBarricades();
        static bool BuildBarricade(const GTA5Offsets::Vec3& position);
        static void BreachSafeZone(SafeZone& zone);
        
        // Utility functions
        static float GetDistanceToPlayer(const GTA5Offsets::Vec3& position);
        static bool HasLineOfSightToPlayer(const GTA5Offsets::Vec3& position);
        static void PlayZombieSound(const GTA5Offsets::Vec3& position, const std::string& sound);
        static void CreateBloodEffect(const GTA5Offsets::Vec3& position);
        
        // Statistics
        static void RecordZombieKill(ZombieType type, bool headshot, bool melee);
        static void UpdateAccuracy(bool hit);
        static void ResetStats();
        
        // Configuration
        static void SetDifficulty(float multiplier);
        static void SetMaxZombies(int count);
        static void EnableZombieType(ZombieType type, bool enabled);
    };
    
    // Zombie type configurations
    namespace ZombieConfigs {
        struct ZombieTypeConfig {
            float health;
            float speed;
            float damage;
            float detectionRange;
            float attackRange;
            std::string modelName;
            uint32_t modelHash;
        };
        
        extern const ZombieTypeConfig WALKER_CONFIG;
        extern const ZombieTypeConfig RUNNER_CONFIG;
        extern const ZombieTypeConfig CRAWLER_CONFIG;
        extern const ZombieTypeConfig BRUTE_CONFIG;
        extern const ZombieTypeConfig SPITTER_CONFIG;
        extern const ZombieTypeConfig SCREAMER_CONFIG;
        extern const ZombieTypeConfig BOSS_CONFIG;
        
        const ZombieTypeConfig& GetConfig(ZombieType type);
    }
    
    // Hotkey system for zombie survival
    namespace ZombieHotkeys {
        void ProcessHotkeys();
        void ShowSurvivalMenu();
        void ShowStats();
        void ShowWaveInfo();
    }
}
