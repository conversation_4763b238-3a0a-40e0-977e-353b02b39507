#include "BasicMods.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <Windows.h>

// Console application for testing GTA V mods
// In a real implementation, this would be a DLL injected into GTA V

void PrintHelp() {
    std::cout << "\n=== GTA V Offline Mods - Help Menu ===" << std::endl;
    std::cout << "F1  - Show this help menu" << std::endl;
    std::cout << "F2  - Toggle God Mode" << std::endl;
    std::cout << "F3  - Toggle Infinite Ammo" << std::endl;
    std::cout << "F4  - Toggle Super Speed" << std::endl;
    std::cout << "F5  - Toggle Vehicle God Mode" << std::endl;
    std::cout << "F6  - Repair Current Vehicle" << std::endl;
    std::cout << "F7  - Add $100,000" << std::endl;
    std::cout << "F8  - Clear Wanted Level" << std::endl;
    std::cout << "F9  - Teleport to Airport" << std::endl;
    std::cout << "F10 - Change Weather" << std::endl;
    std::cout << "F11 - Set Time to Noon" << std::endl;
    std::cout << "F12 - Exit" << std::endl;
    std::cout << "======================================\n" << std::endl;
}

void PrintStatus() {
    std::cout << "\n=== Current Mod Status ===" << std::endl;
    std::cout << "God Mode: " << (BasicMods::Player::GodMode::IsEnabled() ? "ON" : "OFF") << std::endl;
    std::cout << "Infinite Ammo: " << (BasicMods::Player::InfiniteAmmo::IsEnabled() ? "ON" : "OFF") << std::endl;
    std::cout << "Super Speed: " << (BasicMods::Player::SuperSpeed::IsEnabled() ? "ON" : "OFF");
    if (BasicMods::Player::SuperSpeed::IsEnabled()) {
        std::cout << " (x" << BasicMods::Player::SuperSpeed::GetSpeedMultiplier() << ")";
    }
    std::cout << std::endl;
    std::cout << "Vehicle God Mode: " << (BasicMods::Vehicle::VehicleGodMode::IsEnabled() ? "ON" : "OFF") << std::endl;
    
    // Show current money
    int32_t money = BasicMods::Player::MoneyMod::GetMoney();
    std::cout << "Money: $" << money << std::endl;
    
    // Show current position
    auto pos = BasicMods::Player::Teleport::GetPosition();
    std::cout << "Position: (" << pos.x << ", " << pos.y << ", " << pos.z << ")" << std::endl;
    
    // Show wanted level
    int wantedLevel = BasicMods::Player::WantedLevel::GetWantedLevel();
    std::cout << "Wanted Level: " << wantedLevel << std::endl;
    
    std::cout << "==========================\n" << std::endl;
}

// Hotkey processing
void ProcessHotkeys() {
    // F1 - Help
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F1)) {
        PrintHelp();
    }
    
    // F2 - God Mode
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F2)) {
        BasicMods::Player::GodMode::Toggle();
        PrintStatus();
    }
    
    // F3 - Infinite Ammo
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F3)) {
        BasicMods::Player::InfiniteAmmo::Toggle();
        PrintStatus();
    }
    
    // F4 - Super Speed
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F4)) {
        BasicMods::Player::SuperSpeed::Toggle();
        PrintStatus();
    }
    
    // F5 - Vehicle God Mode
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F5)) {
        BasicMods::Vehicle::VehicleGodMode::Toggle();
        PrintStatus();
    }
    
    // F6 - Repair Vehicle
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F6)) {
        if (BasicMods::Vehicle::VehicleRepair::RepairCurrentVehicle()) {
            std::cout << "[INFO] Vehicle repaired!" << std::endl;
        } else {
            std::cout << "[WARNING] No vehicle to repair or repair failed!" << std::endl;
        }
    }
    
    // F7 - Add Money
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F7)) {
        BasicMods::Player::MoneyMod::AddMoney(100000);
        PrintStatus();
    }
    
    // F8 - Clear Wanted Level
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F8)) {
        BasicMods::Player::WantedLevel::ClearWantedLevel();
        PrintStatus();
    }
    
    // F9 - Teleport to Airport
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F9)) {
        BasicMods::Player::Teleport::TeleportToAirport();
        PrintStatus();
    }
    
    // F10 - Change Weather
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F10)) {
        BasicMods::World::WeatherControl::CycleWeather();
        std::cout << "[INFO] Weather changed!" << std::endl;
    }
    
    // F11 - Set Time to Noon
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F11)) {
        BasicMods::World::TimeControl::SetNoon();
        std::cout << "[INFO] Time set to noon!" << std::endl;
    }
    
    // F12 - Exit
    if (BasicMods::Hotkeys::IsKeyJustPressed(BasicMods::Hotkeys::F12)) {
        std::cout << "[INFO] Exiting GTA V Offline Mods..." << std::endl;
        exit(0);
    }
}

// Main update loop
void UpdateMods() {
    // Update all active mods
    BasicMods::Player::GodMode::Update();
    BasicMods::Player::InfiniteAmmo::Update();
    BasicMods::Vehicle::VehicleGodMode::Update();
    
    // Process hotkeys
    ProcessHotkeys();
    
    // Safety check
    BasicMods::SafetyCheck();
}

int main() {
    std::cout << "=== GTA V Offline Mods v1.0 ===" << std::endl;
    std::cout << "Compatible with GTA V version 1.0.3586.0" << std::endl;
    std::cout << "OFFLINE SINGLE-PLAYER USE ONLY!" << std::endl;
    std::cout << "================================\n" << std::endl;
    
    // Initialize mods
    if (!BasicMods::InitializeMods()) {
        std::cout << "[ERROR] Failed to initialize mods! Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Show initial help
    PrintHelp();
    PrintStatus();
    
    std::cout << "[INFO] Mods are now active! Use hotkeys to control features." << std::endl;
    std::cout << "[INFO] The application will run in the background." << std::endl;
    std::cout << "[WARNING] Only use in offline/single-player mode!" << std::endl;
    
    // Main loop
    while (true) {
        UpdateMods();
        
        // Sleep to prevent excessive CPU usage
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    return 0;
}

// DLL Entry Point (for when compiled as DLL)
#ifdef _DLL
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Initialize mods when DLL is loaded
        CreateThread(nullptr, 0, [](LPVOID) -> DWORD {
            if (BasicMods::InitializeMods()) {
                // Main mod loop
                while (true) {
                    UpdateMods();
                    std::this_thread::sleep_for(std::chrono::milliseconds(50));
                }
            }
            return 0;
        }, nullptr, 0, nullptr);
        break;
        
    case DLL_PROCESS_DETACH:
        // Cleanup when DLL is unloaded
        BasicMods::Player::GodMode::Disable();
        BasicMods::Player::InfiniteAmmo::Disable();
        BasicMods::Player::SuperSpeed::Disable();
        BasicMods::Vehicle::VehicleGodMode::Disable();
        break;
    }
    return TRUE;
}
#endif
